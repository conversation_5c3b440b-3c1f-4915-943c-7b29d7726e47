package com.chief.service;

import org.apache.spark.api.java.JavaRDD;
import org.apache.spark.api.java.JavaSparkContext;
import org.apache.spark.sql.Dataset;
import org.apache.spark.sql.Row;
import org.apache.spark.sql.SparkSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Service
public class SparkService {

    @Autowired
    private JavaSparkContext javaSparkContext;

    @Autowired
    private SparkSession sparkSession;

    /**
     * 简单的RDD操作示例
     */
    public List<String> processTextData(List<String> inputData) {
        JavaRDD<String> rdd = javaSparkContext.parallelize(inputData);
        
        // 转换为大写并过滤长度大于3的字符串
        JavaRDD<String> processedRdd = rdd
                .map(String::toUpperCase)
                .filter(s -> s.length() > 3);
        
        return processedRdd.collect();
    }

    /**
     * 使用SparkSQL处理数据
     */
    public List<Row> processWithSQL() {
        // 创建示例数据
        List<String> data = Arrays.asList(
            "<PERSON>,25,Engineer",
            "<PERSON>,30,Manager", 
            "<PERSON>,35,Developer",
            "<PERSON>,28,Analyst"
        );
        
        JavaRDD<String> rdd = javaSparkContext.parallelize(data);
        
        // 将RDD转换为DataFrame
        JavaRDD<Row> rowRDD = rdd.map(line -> {
            String[] parts = line.split(",");
            return org.apache.spark.sql.RowFactory.create(
                parts[0], 
                Integer.parseInt(parts[1]), 
                parts[2]
            );
        });
        
        // 定义schema
        org.apache.spark.sql.types.StructType schema = new org.apache.spark.sql.types.StructType()
                .add("name", org.apache.spark.sql.types.DataTypes.StringType)
                .add("age", org.apache.spark.sql.types.DataTypes.IntegerType)
                .add("job", org.apache.spark.sql.types.DataTypes.StringType);
        
        Dataset<Row> df = sparkSession.createDataFrame(rowRDD, schema);
        
        // 注册临时视图
        df.createOrReplaceTempView("people");

        // 执行SQL查询
        Dataset<Row> result = sparkSession.sql("SELECT name, age FROM people WHERE age > 27 ORDER BY age");
        
        return result.collectAsList();
    }

    /**
     * 计算数字列表的统计信息
     */
    public String calculateStatistics(List<Integer> numbers) {
        JavaRDD<Integer> rdd = javaSparkContext.parallelize(numbers);
        
        long count = rdd.count();
        double sum = rdd.reduce(Integer::sum);
        double mean = sum / count;
        
        return String.format("Count: %d, Sum: %.0f, Mean: %.2f", count, sum, mean);
    }

    /**
     * 词频统计示例
     */
    public List<scala.Tuple2<String, Integer>> wordCount(String text) {
        JavaRDD<String> words = javaSparkContext.parallelize(Arrays.asList(text.split("\\s+")));
        
        return words
                .mapToPair(word -> new scala.Tuple2<>(word.toLowerCase(), 1))
                .reduceByKey(Integer::sum)
                .collect();
    }
}
