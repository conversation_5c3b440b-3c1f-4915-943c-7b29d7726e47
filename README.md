# Spring Boot 3 + Apache Spark 集成项目

这个项目演示了如何在Spring Boot 3应用中集成Apache Spark，使用本地Spark模式进行数据处理。

## 项目特性

- Spring Boot 3.2.0
- Apache Spark 3.5.0 (本地模式)
- Java 21
- Maven构建
- RESTful API接口
- 单元测试

## 项目结构

```
src/
├── main/
│   ├── java/
│   │   └── com/chief/
│   │       ├── SparkSpringBootApplication.java  # 主应用类
│   │       ├── config/
│   │       │   └── SparkConfig.java             # Spark配置
│   │       ├── service/
│   │       │   └── SparkService.java            # Spark服务类
│   │       └── controller/
│   │           └── SparkController.java         # REST控制器
│   └── resources/
│       └── application.yml                      # 应用配置
└── test/
    └── java/
        └── com/chief/
            └── SparkServiceTest.java            # 单元测试
```

## 快速开始

### 1. 构建项目

```bash
mvn clean compile
```

### 2. 运行测试

```bash
mvn test
```

### 3. 启动应用

```bash
mvn spring-boot:run
```

应用将在 http://localhost:8080 启动

## API接口

### 基础测试
- `GET /api/spark/test` - 测试Spark集成是否正常

### 数据处理接口
- `POST /api/spark/process-text` - 处理文本数据（转大写，过滤长度）
- `POST /api/spark/statistics` - 计算数字统计信息
- `POST /api/spark/word-count` - 词频统计
- `GET /api/spark/sql-demo` - SparkSQL演示
- `GET /api/spark/demo` - 综合功能演示

### CSV导入接口
- `POST /api/csv/import/upload` - 通过文件上传导入CSV到MySQL
- `POST /api/csv/import/path` - 通过文件路径导入CSV到MySQL
- `POST /api/csv/analyze` - 分析CSV文件结构
- `GET /api/csv/modes` - 获取支持的导入模式
- `GET /api/csv/health` - CSV导入服务健康检查

## 使用示例

### 1. 文本处理
```bash
curl -X POST http://localhost:8080/api/spark/process-text \
  -H "Content-Type: application/json" \
  -d '["hello", "world", "hi", "spark", "java"]'
```

### 2. 统计计算
```bash
curl -X POST http://localhost:8080/api/spark/statistics \
  -H "Content-Type: application/json" \
  -d '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]'
```

### 3. 词频统计
```bash
curl -X POST http://localhost:8080/api/spark/word-count \
  -H "Content-Type: application/json" \
  -d '"hello world hello spark world java spark"'
```

### 4. 综合演示
```bash
curl http://localhost:8080/api/spark/demo
```

### 5. CSV文件导入MySQL
```bash
# 分析CSV文件结构
curl -X POST http://localhost:8080/api/csv/analyze \
  -F "file=@sample-data/products.csv" \
  -F "hasHeader=true" \
  -F "delimiter=,"

# 导入CSV文件到MySQL
curl -X POST http://localhost:8080/api/csv/import/upload \
  -F "file=@sample-data/products.csv" \
  -F "tableName=products" \
  -F "hasHeader=true" \
  -F "delimiter=," \
  -F "mode=overwrite" \
  -F "columnNames=product_code,product_name,category,brand,price,cost,stock_quantity,created_date,status"

# 通过JSON请求导入（文件路径方式）
curl -X POST http://localhost:8080/api/csv/import/path \
  -H "Content-Type: application/json" \
  -d '{
    "tableName": "sales_data",
    "csvFilePath": "sample-data/sales_data.csv",
    "hasHeader": true,
    "delimiter": ",",
    "mode": "append",
    "columnNames": ["product_name", "category", "price", "quantity", "sale_date", "customer_name", "region"]
  }'
```

## 配置说明

### Spark配置 (application.yml)
```yaml
spark:
  app:
    name: SparkSpringBootApp
  master: local[*]  # 本地模式，使用所有可用CPU核心
```

### 主要配置参数
- `spark.master: local[*]` - 使用本地模式，所有可用核心
- `spark.sql.warehouse.dir` - Spark SQL仓库目录
- `spark.sql.adaptive.enabled` - 启用自适应查询执行
- `spark.serializer` - 使用Kryo序列化器提高性能

## 功能特性

1. **RDD操作** - 基础的RDD转换和行动操作
2. **SparkSQL** - 使用SQL查询结构化数据
3. **统计计算** - 数值统计和聚合操作
4. **文本处理** - 词频统计等文本分析
5. **Spring集成** - 完整的Spring Boot集成，支持依赖注入

## 注意事项

1. 本项目使用Spark本地模式，适合开发和测试
2. 生产环境建议使用Spark集群模式
3. 确保有足够的内存运行Spark应用
4. 日志级别已调整，减少Spark相关的冗余日志

## CSV导入MySQL功能

### 功能特性
- **文件上传导入** - 支持通过Web界面上传CSV文件
- **路径导入** - 支持通过指定文件路径导入CSV
- **智能分析** - 自动分析CSV文件结构和列信息
- **多种模式** - 支持append、overwrite、ignore、error四种导入模式
- **列映射** - 支持自定义列名映射
- **实时反馈** - 提供详细的导入进度和结果信息

### 使用步骤
1. 启动应用程序：`mvn spring-boot:run`
2. 访问 http://localhost:8080
3. 在"CSV导入MySQL"部分选择CSV文件
4. 配置导入参数（表名、分隔符、模式等）
5. 点击"分析CSV文件"查看文件结构
6. 点击"导入CSV到MySQL"执行导入

### 数据库配置
在使用CSV导入功能前，请确保：
1. MySQL服务器正在运行
2. 数据库`spark_demo`已创建
3. 配置正确的数据库连接信息（application.yml）

```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS spark_demo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 示例数据
项目提供了示例CSV文件：
- `sample-data/sales_data.csv` - 销售数据示例
- `sample-data/products.csv` - 产品信息示例
- `src/test/resources/test-data.csv` - 员工信息示例

## 扩展建议

1. **数据源扩展** - 添加更多数据源支持（HDFS、数据库、API等）
2. **实时处理** - 集成Spark Streaming进行实时数据处理
3. **机器学习** - 添加MLlib机器学习功能
4. **集群模式** - 配置Spark集群模式支持
5. **数据可视化** - 添加数据可视化和报表功能
6. **数据质量** - 添加数据验证和清洗功能
7. **调度系统** - 集成任务调度和监控系统
