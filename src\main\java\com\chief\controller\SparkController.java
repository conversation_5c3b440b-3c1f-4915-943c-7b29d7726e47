package com.chief.controller;

import com.chief.service.SparkService;
import org.apache.spark.sql.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/spark")
public class SparkController {

    @Autowired
    private SparkService sparkService;

    @GetMapping("/test")
    public String test() {
        return "Spark integration is working!";
    }

    @PostMapping("/process-text")
    public List<String> processText(@RequestBody List<String> inputData) {
        return sparkService.processTextData(inputData);
    }

    @GetMapping("/sql-demo")
    public List<Map<String, Object>> sqlDemo() {
        List<Row> rows = sparkService.processWithSQL();

        return rows.stream()
                .map(row -> {
                    Map<String, Object> result = new java.util.HashMap<>();
                    result.put("name", row.getString(0));
                    result.put("age", row.getInt(1));
                    return result;
                })
                .collect(Collectors.toList());
    }

    @PostMapping("/statistics")
    public String calculateStatistics(@RequestBody List<Integer> numbers) {
        return sparkService.calculateStatistics(numbers);
    }

    @PostMapping("/word-count")
    public List<Map<String, Object>> wordCount(@RequestBody String text) {
        List<scala.Tuple2<String, Integer>> result = sparkService.wordCount(text);

        return result.stream()
                .map(tuple -> {
                    Map<String, Object> wordCount = new java.util.HashMap<>();
                    wordCount.put("word", tuple._1());
                    wordCount.put("count", tuple._2());
                    return wordCount;
                })
                .collect(Collectors.toList());
    }

    @GetMapping("/demo")
    public Map<String, Object> demo() {
        // 演示各种功能
        List<String> textData = Arrays.asList("hello", "world", "spark", "springboot", "java");
        List<String> processedText = sparkService.processTextData(textData);
        
        List<Integer> numbers = Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10);
        String statistics = sparkService.calculateStatistics(numbers);
        
        String sampleText = "hello world hello spark world java spark";
        List<scala.Tuple2<String, Integer>> wordCount = sparkService.wordCount(sampleText);
        
        List<Row> sqlResult = sparkService.processWithSQL();
        
        Map<String, Object> result = new java.util.HashMap<>();
        result.put("processedText", processedText);
        result.put("statistics", statistics);
        result.put("wordCount", wordCount.stream()
                .map(tuple -> {
                    Map<String, Object> wc = new java.util.HashMap<>();
                    wc.put("word", tuple._1());
                    wc.put("count", tuple._2());
                    return wc;
                })
                .collect(Collectors.toList()));
        result.put("sqlResult", sqlResult.stream()
                .map(row -> {
                    Map<String, Object> sr = new java.util.HashMap<>();
                    sr.put("name", row.getString(0));
                    sr.put("age", row.getInt(1));
                    return sr;
                })
                .collect(Collectors.toList()));
        return result;
    }
}
