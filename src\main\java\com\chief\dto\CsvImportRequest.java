package com.chief.dto;

import java.util.List;

public class CsvImportRequest {
    private String tableName;
    private List<String> columnNames;
    private String csvFilePath;
    private boolean hasHeader;
    private String delimiter;
    private String mode; // append, overwrite, ignore, error

    public CsvImportRequest() {
        this.hasHeader = true;
        this.delimiter = ",";
        this.mode = "append";
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public List<String> getColumnNames() {
        return columnNames;
    }

    public void setColumnNames(List<String> columnNames) {
        this.columnNames = columnNames;
    }

    public String getCsvFilePath() {
        return csvFilePath;
    }

    public void setCsvFilePath(String csvFilePath) {
        this.csvFilePath = csvFilePath;
    }

    public boolean isHasHeader() {
        return hasHeader;
    }

    public void setHasHeader(boolean hasHeader) {
        this.hasHeader = hasHeader;
    }

    public String getDelimiter() {
        return delimiter;
    }

    public void setDelimiter(String delimiter) {
        this.delimiter = delimiter;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    @Override
    public String toString() {
        return "CsvImportRequest{" +
                "tableName='" + tableName + '\'' +
                ", columnNames=" + columnNames +
                ", csvFilePath='" + csvFilePath + '\'' +
                ", hasHeader=" + hasHeader +
                ", delimiter='" + delimiter + '\'' +
                ", mode='" + mode + '\'' +
                '}';
    }
}
