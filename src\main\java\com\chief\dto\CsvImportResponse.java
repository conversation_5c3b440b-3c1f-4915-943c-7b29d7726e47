package com.chief.dto;

import java.time.LocalDateTime;
import java.util.List;

public class CsvImportResponse {
    private boolean success;
    private String message;
    private long recordsProcessed;
    private long recordsInserted;
    private long recordsSkipped;
    private long processingTimeMs;
    private LocalDateTime timestamp;
    private List<String> errors;
    private String tableName;

    public CsvImportResponse() {
        this.timestamp = LocalDateTime.now();
    }

    public static CsvImportResponse success(String tableName, long recordsProcessed, long recordsInserted, long processingTimeMs) {
        CsvImportResponse response = new CsvImportResponse();
        response.success = true;
        response.tableName = tableName;
        response.recordsProcessed = recordsProcessed;
        response.recordsInserted = recordsInserted;
        response.processingTimeMs = processingTimeMs;
        response.message = String.format("Successfully imported %d records to table '%s' in %d ms", 
                recordsInserted, tableName, processingTimeMs);
        return response;
    }

    public static CsvImportResponse failure(String message, List<String> errors) {
        CsvImportResponse response = new CsvImportResponse();
        response.success = false;
        response.message = message;
        response.errors = errors;
        return response;
    }

    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public long getRecordsProcessed() {
        return recordsProcessed;
    }

    public void setRecordsProcessed(long recordsProcessed) {
        this.recordsProcessed = recordsProcessed;
    }

    public long getRecordsInserted() {
        return recordsInserted;
    }

    public void setRecordsInserted(long recordsInserted) {
        this.recordsInserted = recordsInserted;
    }

    public long getRecordsSkipped() {
        return recordsSkipped;
    }

    public void setRecordsSkipped(long recordsSkipped) {
        this.recordsSkipped = recordsSkipped;
    }

    public long getProcessingTimeMs() {
        return processingTimeMs;
    }

    public void setProcessingTimeMs(long processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public List<String> getErrors() {
        return errors;
    }

    public void setErrors(List<String> errors) {
        this.errors = errors;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }
}
