package com.chief;

import com.chief.service.SparkService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class SparkServiceTest {

    @Autowired
    private SparkService sparkService;

    @Test
    public void testProcessTextData() {
        List<String> inputData = Arrays.asList("hello", "world", "hi", "spark");
        List<String> result = sparkService.processTextData(inputData);
        
        // 应该过滤掉长度<=3的字符串，并转换为大写
        assertEquals(3, result.size());
        assertTrue(result.contains("HELLO"));
        assertTrue(result.contains("WORLD"));
        assertTrue(result.contains("SPARK"));
        assertFalse(result.contains("HI")); // 长度<=3，应该被过滤掉
    }

    @Test
    public void testCalculateStatistics() {
        List<Integer> numbers = Arrays.asList(1, 2, 3, 4, 5);
        String result = sparkService.calculateStatistics(numbers);
        
        assertTrue(result.contains("Count: 5"));
        assertTrue(result.contains("Sum: 15"));
        assertTrue(result.contains("Mean: 3.00"));
    }

    @Test
    public void testWordCount() {
        String text = "hello world hello spark";
        List<scala.Tuple2<String, Integer>> result = sparkService.wordCount(text);
        
        assertEquals(3, result.size());
        
        // 验证词频统计结果
        boolean foundHello = false, foundWorld = false, foundSpark = false;
        for (scala.Tuple2<String, Integer> tuple : result) {
            if ("hello".equals(tuple._1()) && tuple._2() == 2) {
                foundHello = true;
            } else if ("world".equals(tuple._1()) && tuple._2() == 1) {
                foundWorld = true;
            } else if ("spark".equals(tuple._1()) && tuple._2() == 1) {
                foundSpark = true;
            }
        }
        
        assertTrue(foundHello);
        assertTrue(foundWorld);
        assertTrue(foundSpark);
    }
}
